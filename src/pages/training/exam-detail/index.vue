<script setup lang="ts">
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '@/api/api.model'
import type { TrainingRecord } from '@/hooks/useTrainingData'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  V1LocationStudyCheckProjectIdUserId,
  V1ManageTrainStudyRecordsPagePost,
} from '@/api/api.req'
import NoData from '@/components/NoData.vue'
import TrainingRecords from '@/components/TrainingRecords.vue'
import { fetchTabDataCommon } from '@/hooks/useTrainingData'
import { formatDuration } from '@/utils/time'

// 获取路由参数
const route = useRoute()
const userId = ref(route.query.userId as string || '')

// 选项卡配置
const tabs = ref<V1LocationStudyCheckProjectIDUserIDGetResponseResult[]>([])
const activeTab = ref('')
const activeTabIndex = ref(0)

// 考核数据
const examData = ref({
  examTime: '',
  requirement: '',
  status: '',
  statusText: '',
  operationPassRate: 0,
  actionPassRate: 0,
  duration: '',
  validOperations: 0,
  failedOperations: 0,
  failedActions: 0,
})

// 考核记录列表
const examRecords = ref<TrainingRecord[]>([])

// 获取标签数据
async function fetchTabs() {
  try {
    const response = await V1ManageTrainStudyRecordsPagePost({
      data: {
        projectType: 2, // 考核项目类型为2
        userId: userId.value,
      },
    })
    if (response?.records) {
      tabs.value = response.records || []
      if (tabs.value.length > 0 && !activeTab.value) {
        activeTab.value = tabs.value[0].id || ''
        activeTabIndex.value = 0
        await fetchTabData(tabs.value[0].id || '')
      }
      // 更新考核数据
      const currentTab = tabs.value.find(item => item.id === activeTab.value)
      if (currentTab?.projectId) {
        await fetchCompetitionInfo(currentTab.projectId)
      }
    }
  }
  catch (error) {
    console.error('获取标签数据失败:', error)
  }
}

// 切换选项卡
function handleTabChange(index: number) {
  if (tabs.value[index]) {
    const tab = tabs.value[index]
    activeTab.value = tab.id || ''
    activeTabIndex.value = index
    fetchTabData(tab.id || '')
    if (tab.projectId) {
      fetchCompetitionInfo(tab.projectId)
    }
  }
}

// 获取考核记录数据
async function fetchTabData(tabId: string) {
  if (!tabId)
    return
  await fetchTabDataCommon(tabId, (records) => {
    examRecords.value = records
  })
}

// 获取考核信息
async function fetchCompetitionInfo(projectId: string) {
  if (!projectId)
    return

  try {
    const response = await V1LocationStudyCheckProjectIdUserId({
      projectId,
      userId: userId.value,
    })

    if (response) {
      const data = response
      examData.value = {
        examTime: data.lastUpdateTime || '',
        requirement: `${data.requestDuration || 0} min 内完成${data.requestFrequency || 0
        }次操作且合格率在 ${data.requestQualificationRate || 0}% 和动作达标率在 ${data.requestActionRate
        }% 以上`,
        status: data.status || '',
        statusText: data.status === 'PASSED_CHECK' ? '合格' : '不合格',
        operationPassRate: Math.floor((data.opNumOk! / data.opNum!) * 100 || 0),
        actionPassRate: Math.floor((data.actionNumOk! / data.actionNum!) * 100 || 0),
        duration: formatDuration(data.trainDuration || 0),
        validOperations: data.effectiveNum || 0,
        failedOperations: (data?.opNum - data?.opNumOk) || 0,
        failedActions: (data?.actionNum - data?.actionNumOk) || 0,
      }
    }
  }
  catch (error) {
    console.error('获取考核信息失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  // 从路由参数获取初始数据
  if (route.query.id) {
    activeTab.value = route.query.id as string
  }

  await fetchTabs()

  if (route.query.id) {
    await fetchTabData(route.query.id as string)
    // 设置当前选中的标签索引
    const index = tabs.value.findIndex(item => item.id === route.query.id)
    if (index >= 0) {
      activeTabIndex.value = index
    }
  }
})
</script>

<template>
  <div class="exam-detail min-h-screen bg-[#F5F7FA]">
    <VanNavBar title="考核详情" left-arrow @click-left="$router.back()" />

    <div class="page-content">
      <!-- 标签导航栏 -->
      <div class="tab-nav border-b border-gray-200 bg-white">
        <VanTabs
          v-model:active="activeTabIndex"
          swipeable
          sticky
          offset-top="46"
          @change="handleTabChange"
        >
          <VanTab
            v-for="(tab, index) in tabs"
            :key="tab.id"
            :title="tab.projectName"
            :name="index"
          />
        </VanTabs>
      </div>

      <!-- 考核信息区域 -->
      <div class="exam-info px-4 py-4">
        <div class="section-title mb-4 flex items-center">
          <span class="text-lg text-gray-800 font-medium">考核信息</span>
        </div>

        <div class="info-grid bg-white px-3 py-2 space-y-3">
          <div class="info-item flex justify-between border-b border-gray-200 py-2">
            <span class="text-sm text-gray-600">考核时间</span>
            <span class="text-sm text-gray-800">{{ examData.examTime || '-' }}</span>
          </div>

          <div class="info-item flex justify-between gap-3 border-b border-gray-200 py-2">
            <span class="text-sm text-gray-600">考核要求</span>
            <span class="flex-1 text-sm text-gray-800">{{ examData.requirement || '-' }}</span>
          </div>

          <div class="info-item flex justify-between py-2">
            <span class="text-sm text-gray-600">考核状态</span>
            <span
              class="text-sm font-medium"
              :class="examData.status === 'PASSED_CHECK' ? 'text-[#00996b]' : 'text-red-500'"
            >
              {{ examData.statusText }}
            </span>
          </div>
        </div>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section px-4 py-4">
        <div class="section-title mb-4 flex items-center">
          <span class="text-lg text-gray-800 font-medium">数据统计</span>
        </div>

        <div class="stats-grid grid grid-cols-2 gap-4">
          <!-- 左侧: 主要指标卡片 -->
          <div class="grid gap-3">
            <!-- 操作合格率 -->
            <div class="stat-card border border-gray-100 rounded-lg bg-white p-4">
              <div class="w-full flex items-center justify-between">
                <div class="flex-1">
                  <div class="mb-1 text-sm text-gray-600">
                    操作合格率
                  </div>
                  <div class="text-xl text-gray-800 font-semibold">
                    {{ examData.operationPassRate }}%
                  </div>
                </div>
                <div class="ml-2">
                  <VanCircle
                    :current-rate="examData.operationPassRate"
                    color="#05C07F"
                    size="70px"
                    :stroke-width="80"
                    :text="`${examData.operationPassRate}%`"
                  />
                </div>
              </div>
            </div>

            <!-- 动作达标率 -->
            <div class="stat-card border border-gray-100 rounded-lg bg-white p-4">
              <div class="w-full flex items-center justify-between">
                <div class="flex-1">
                  <div class="mb-1 text-sm text-gray-600">
                    动作达标率
                  </div>
                  <div class="text-xl text-gray-800 font-semibold">
                    {{ examData.actionPassRate }}%
                  </div>
                </div>
                <div class="ml-2">
                  <VanCircle
                    :current-rate="examData.actionPassRate"
                    color="#4080FF"
                    size="70px"
                    :stroke-width="80"
                    :text="`${examData.actionPassRate}%`"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧: 数字统计区 -->
          <div class="stat-card border border-gray-100 rounded-lg bg-white p-4">
            <div class="space-y-3">
              <!-- 考核用时 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">考核用时</span>
                <span class="text-sm text-gray-800 font-medium">{{ examData.duration || '-' }}</span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 有效作业次数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">有效作业次数</span>
                <span class="text-sm text-gray-800 font-medium">{{ examData.validOperations || 0 }}</span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 不合格次数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">不合格次数</span>
                <span class="text-sm text-red-500 font-medium">{{ examData.failedOperations || 0 }}</span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 动作不达标数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">动作不达标数</span>
                <span class="text-sm text-red-500 font-medium">{{ examData.failedActions || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 考核记录列表 -->
      <TrainingRecords :records="examRecords" title="考核记录" />

      <!-- 空数据处理 -->
      <NoData v-if="examRecords.length === 0" size="small" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.exam-detail {
  .stat-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }
}
</style>
