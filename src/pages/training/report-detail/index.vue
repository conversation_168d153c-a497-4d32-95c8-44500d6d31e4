<script setup lang="ts">
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '@/api/api.model'
import type { TrainingRecord } from '@/hooks/useTrainingData'
import { clamp } from 'lodash-es'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { V1ManageTrainStudyRecordsPagePost } from '@/api/api.req'
import NoData from '@/components/NoData.vue'
import TrainingRecords from '@/components/TrainingRecords.vue'
import { fetchTabDataCommon } from '@/hooks/useTrainingData'
import { toFixed } from '@/utils'
import { formatDuration } from '@/utils/time'

// 获取路由参数
const route = useRoute()
const userId = ref(route.query.userId as string || '')

// 选项卡配置
const tabs = ref<V1LocationStudyCheckProjectIDUserIDGetResponseResult[]>([])
const activeTab = ref('')
const activeTabIndex = ref(0)
const trainingData = ref<any>({
  passRate: 0,
  actionPassRate: 0,
  trainNum: 0,
  opNum: 0,
  opNumOk: 0,
  actionNum: 0,
  actionNumOk: 0,
  trainDuration: 0,
  requestDuration: 0,
  requestFrequency: 0,
  numRate: 0,
  durationRate: 0,
  trainDurationSecond: 0,
  duration: '',
})

const trainData = computed(() => {
  const data = { ...trainingData.value }
  data.passRate = Math.floor(Number(data.passRate))
  data.actionPassRate = Math.floor(Number(data.actionPassRate))
  const p = toFixed((data?.opNum || 0) / (data?.requestFrequency || 1) * 100, 2)
  data.numRate = clamp(p, 0, 100)
  data.durationRate = clamp(
    toFixed((data?.trainDuration || 0) / (data?.requestDuration || 1) * 100, 2),
    0,
    100,
  )
  data.duration = formatDuration(data?.trainDurationSecond || 0)
  return data
})

// 获取标签数据
async function fetchTabs() {
  try {
    const response = await V1ManageTrainStudyRecordsPagePost({
      data: {
        projectType: 1,
        userId: userId.value,
      },
    })
    if (response?.records) {
      tabs.value = response.records || []
      if (tabs.value.length > 0 && !activeTab.value) {
        activeTab.value = tabs.value[0].id || ''
        activeTabIndex.value = 0
        await fetchTabData(tabs.value[0].id || '')
      }
      const currentTab = tabs.value.find(item => item.id === activeTab.value)
      if (currentTab) {
        trainingData.value = currentTab
      }
    }
  }
  catch (error) {
    console.error('获取标签数据失败:', error)
  }
}

const trainingRecords = ref<TrainingRecord[]>([])

// 切换选项卡
function handleTabChange(index: number) {
  if (tabs.value[index]) {
    const tab = tabs.value[index]
    activeTab.value = tab.id || ''
    activeTabIndex.value = index
    trainingData.value = tab
    fetchTabData(tab.id || '')
  }
}

// 获取训练记录数据
async function fetchTabData(tabId: string) {
  if (!tabId)
    return
  await fetchTabDataCommon(tabId, (records) => {
    trainingRecords.value = records
  })
}

// 页面初始化
onMounted(async () => {
  // 从路由参数获取初始数据
  if (route.query.id) {
    activeTab.value = route.query.id as string
  }

  await fetchTabs()

  if (route.query.id) {
    await fetchTabData(route.query.id as string)
    // 设置当前选中的标签索引
    const index = tabs.value.findIndex(item => item.id === route.query.id)
    if (index >= 0) {
      activeTabIndex.value = index
    }
  }
})
</script>

<template>
  <div class="report-detail min-h-screen bg-[#F5F7FA]">
    <VanNavBar title="训练报告详情" left-arrow @click-left="$router.back()" />

    <div class="page-content">
      <!-- 标签导航栏 -->
      <div class="tab-nav border-b border-gray-200 bg-white">
        <VanTabs
          v-model:active="activeTabIndex"
          swipeable
          sticky
          offset-top="46"
          title-active-color="#00996B"
          @change="handleTabChange"
        >
          <VanTab
            v-for="(tab, index) in tabs"
            :key="tab.id"
            :title="tab.projectName"
            :name="index"
          />
        </VanTabs>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section px-4 py-4">
        <div class="section-title mb-4 flex items-center">
          <span class="text-lg text-gray-800 font-medium">
            数据统计
          </span>
        </div>

        <div class="stats-grid grid grid-cols-2 gap-4">
          <!-- 左侧: 主要指标卡片 -->
          <div class="grid gap-3">
            <!-- 总体合格率 -->
            <div class="stat-card border border-gray-100 rounded-lg bg-white p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="mb-1 text-sm text-gray-600">
                    总体合格率
                  </div>
                  <div class="text-xl text-gray-800 font-semibold">
                    {{ trainData.passRate }}%
                  </div>
                </div>
                <div class="ml-2">
                  <VanCircle
                    :current-rate="trainData.passRate"
                    color="#05C07F"
                    layer-color="#e5e6eb"
                    size="70px"
                    :stroke-width="80"
                    :text="`${trainData.passRate}%`"
                  />
                </div>
              </div>
            </div>

            <!-- 动作达标率 -->
            <div class="stat-card border border-gray-100 rounded-lg bg-white p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="mb-1 text-sm text-gray-600">
                    动作达标率
                  </div>
                  <div class="text-xl text-gray-800 font-semibold">
                    {{ trainData.actionPassRate }}%
                  </div>
                </div>
                <div class="ml-2">
                  <VanCircle
                    :current-rate="trainData.actionPassRate"
                    color="#4080ff"
                    layer-color="#e5e6eb"
                    size="70px"
                    :stroke-width="80"
                    :text="`${trainData.actionPassRate}%`"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧: 数字统计区 -->
          <div class="stat-card border border-gray-100 rounded-lg bg-white p-3">
            <div class="space-y-3">
              <!-- 训练次数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">训练次数</span>
                <span class="text-sm text-gray-800 font-medium">
                  {{ trainData.trainNum || 0 }}
                </span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 累计作业次数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">累计作业次数</span>
                <span class="text-sm text-gray-800 font-medium">
                  {{ trainData.opNum || 0 }}
                </span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 不合格次数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">不合格次数</span>
                <span class="text-sm text-red-500 font-medium">
                  {{ (trainData.opNum - trainData.opNumOk) || 0 }}
                </span>
              </div>
              <div class="h-px bg-gray-200" />

              <!-- 动作不达标数 -->
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">动作不达标数</span>
                <span class="text-sm text-red-500 font-medium">
                  {{ (trainData.actionNum - trainData.actionNumOk) || 0 }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练进度区域 -->
      <div class="progress-section px-4 py-4">
        <div class="section-title mb-4 text-lg text-gray-800 font-medium">
          训练进度
        </div>

        <div class="progress-grid grid grid-cols-2 gap-4">
          <!-- 训练时长进度 -->
          <div class="progress-card border border-gray-100 rounded-lg bg-white p-4 text-center">
            <div class="mb-2 text-sm text-gray-600">
              训练时长
            </div>
            <div class="text-primary text-xl font-medium">
              {{ trainData.duration }}
            </div>
          </div>

          <!-- 训练次数进度 -->
          <div class="progress-card border border-gray-100 rounded-lg bg-white p-4">
            <div class="mb-2 text-sm text-gray-600">
              训练进度
            </div>
            <div class="mb-2 text-xl text-gray-800 font-medium">
              {{ trainData.numRate }}%
            </div>
            <div class="mb-2 h-2 overflow-hidden rounded-full bg-gray-200">
              <div
                class="h-full bg-blue-500 transition-all duration-300"
                :style="{ width: `${trainData.numRate}%` }"
              />
            </div>
            <div class="text-xs text-gray-500">
              <span class="text-blue-500">{{ trainData.opNum || 0 }}次</span>
              <span> / {{ trainData.requestFrequency || 0 }}次</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练记录列表 -->
      <TrainingRecords :records="trainingRecords" />

      <!-- 空数据处理 -->
      <NoData v-if="trainingRecords.length === 0" size="small" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.report-detail {
  .stat-card,
  .progress-card {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }

  .progress-card {
    &:active {
      transform: scale(0.98);
    }
  }
}
</style>
